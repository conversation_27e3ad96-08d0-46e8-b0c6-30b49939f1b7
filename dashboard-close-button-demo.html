<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Dashboard Close Button Demo</title>
  <style>
    /* CSS Variables */
    :root {
      --background-color: #1a1a1a;
      --text-color: #ffffff;
      --text-secondary: #cccccc;
      --danger-color: #dc3545;
      --transition-normal: all 0.3s ease;
      --border-color: #333;
    }
    
    body {
      margin: 0;
      padding: 20px;
      font-family: Arial, sans-serif;
      background: var(--background-color);
      color: var(--text-color);
    }
    
    /* Dashboard Editor Styles */
    .chart-editor {
      width: 100%;
      height: 500px;
      background: #2d2d2d;
      border: 1px solid var(--border-color);
      border-radius: 8px;
      position: relative;
      display: none;
      padding: 20px;
    }
    
    .chart-editor.fullscreen {
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      z-index: 9999;
      background: var(--background-color);
      border-radius: 0;
      border: none;
    }
    
    /* Fullscreen close button (exact copy from dashboard CSS) */
    .fullscreen-close-button {
      position: fixed;
      top: 20px;
      left: 20px;
      z-index: 10001;
      background: none;
      border: none;
      color: var(--text-secondary);
      font-size: 2rem;
      cursor: pointer;
      padding: 0;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      transition: var(--transition-normal);
      backdrop-filter: blur(15px);
      background: rgba(0, 0, 0, 0.7);
    }

    .fullscreen-close-button:hover {
      background: var(--danger-color);
      color: white;
      transform: scale(1.1);
    }

    /* Show fullscreen close button only in fullscreen mode */
    .chart-editor.fullscreen .fullscreen-close-button {
      display: flex !important;
    }
    
    /* View Modal Styles */
    .modal {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      display: none;
      align-items: center;
      justify-content: center;
      z-index: 1000;
    }
    
    .modal.fullscreen {
      background: var(--background-color);
    }
    
    .modal-content {
      background: #2d2d2d;
      border-radius: 8px;
      padding: 20px;
      width: 80%;
      max-width: 600px;
      position: relative;
    }
    
    .modal.fullscreen .modal-content {
      width: 100%;
      height: 100%;
      max-width: none;
      border-radius: 0;
    }
    
    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid var(--border-color);
    }
    
    .close-modal {
      color: var(--text-secondary);
      font-size: 1.5rem;
      font-weight: bold;
      cursor: pointer;
      transition: var(--transition-normal);
    }
    
    .close-modal:hover {
      color: #00d4aa;
    }
    
    /* Enhanced close button styling for fullscreen view modal */
    .modal.fullscreen .close-modal {
      position: fixed;
      top: 20px;
      left: 20px;
      z-index: 10001;
      background: rgba(0, 0, 0, 0.7);
      backdrop-filter: blur(15px);
      border-radius: 50%;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 2rem;
      transition: var(--transition-normal);
    }

    .modal.fullscreen .close-modal:hover {
      background: var(--danger-color);
      color: white;
      transform: scale(1.1);
    }
    
    /* Button styles */
    .btn {
      background: #007acc;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      margin: 10px;
      font-size: 14px;
    }
    
    .btn:hover {
      background: #005a9e;
    }
    
    .demo-section {
      margin-bottom: 40px;
      padding: 20px;
      border: 1px solid var(--border-color);
      border-radius: 8px;
    }
  </style>
</head>
<body>
  <h1>Dashboard Close Button Implementation Demo</h1>
  
  <div class="demo-section">
    <h2>1. Edit Modal Fullscreen</h2>
    <p>This demonstrates the dashboard editor with a close button in fullscreen mode.</p>
    <button class="btn" onclick="showEditor()">Open Editor (Auto Fullscreen)</button>
    <button class="btn" onclick="toggleEditorFullscreen()">Toggle Editor Fullscreen</button>
    
    <div id="chart-editor" class="chart-editor">
      <!-- Fullscreen close button (only visible in fullscreen mode) -->
      <button id="editor-fullscreen-close" class="fullscreen-close-button" style="display: none;" title="Exit Fullscreen">&times;</button>
      
      <div>
        <h3>Dashboard Editor</h3>
        <p>This simulates the dashboard editor interface.</p>
        <p><strong>Expected behavior:</strong></p>
        <ul>
          <li>When in fullscreen mode, you should see a close button (×) in the top-left corner</li>
          <li>The close button should have a dark semi-transparent background</li>
          <li>On hover, it should turn red and scale up slightly</li>
          <li>Clicking it should exit fullscreen mode</li>
        </ul>
        <button class="btn" onclick="hideEditor()">Close Editor</button>
      </div>
    </div>
  </div>
  
  <div class="demo-section">
    <h2>2. View Modal Fullscreen</h2>
    <p>This demonstrates the dashboard view modal with enhanced close button in fullscreen mode.</p>
    <button class="btn" onclick="showModal()">Open View Modal</button>
    <button class="btn" onclick="toggleModalFullscreen()">Toggle Modal Fullscreen</button>
    
    <div id="view-modal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>View Modal</h3>
          <span class="close-modal" onclick="hideModal()">&times;</span>
        </div>
        <div>
          <p>This simulates the dashboard view modal.</p>
          <p><strong>Expected behavior:</strong></p>
          <ul>
            <li>In normal mode, close button is in the header (top-right)</li>
            <li>In fullscreen mode, close button moves to top-left corner with enhanced styling</li>
            <li>Same hover effects as the editor close button</li>
          </ul>
          <button class="btn" onclick="hideModal()">Close Modal</button>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Editor functionality
    function showEditor() {
      const editor = document.getElementById('chart-editor');
      const closeButton = document.getElementById('editor-fullscreen-close');
      
      editor.style.display = 'block';
      // Auto-enter fullscreen (like the real dashboard)
      editor.classList.add('fullscreen');
      closeButton.style.display = 'flex';
      document.body.style.overflow = 'hidden';
      
      console.log('Editor opened in fullscreen mode');
    }
    
    function hideEditor() {
      const editor = document.getElementById('chart-editor');
      const closeButton = document.getElementById('editor-fullscreen-close');
      
      editor.style.display = 'none';
      editor.classList.remove('fullscreen');
      closeButton.style.display = 'none';
      document.body.style.overflow = '';
      
      console.log('Editor closed');
    }
    
    function toggleEditorFullscreen() {
      const editor = document.getElementById('chart-editor');
      const closeButton = document.getElementById('editor-fullscreen-close');
      
      if (editor.classList.contains('fullscreen')) {
        editor.classList.remove('fullscreen');
        closeButton.style.display = 'none';
        document.body.style.overflow = '';
        console.log('Editor exited fullscreen');
      } else {
        editor.classList.add('fullscreen');
        closeButton.style.display = 'flex';
        document.body.style.overflow = 'hidden';
        console.log('Editor entered fullscreen');
      }
    }
    
    // Modal functionality
    function showModal() {
      const modal = document.getElementById('view-modal');
      modal.style.display = 'flex';
      console.log('Modal opened');
    }
    
    function hideModal() {
      const modal = document.getElementById('view-modal');
      modal.style.display = 'none';
      modal.classList.remove('fullscreen');
      document.body.style.overflow = '';
      console.log('Modal closed');
    }
    
    function toggleModalFullscreen() {
      const modal = document.getElementById('view-modal');
      
      if (modal.classList.contains('fullscreen')) {
        modal.classList.remove('fullscreen');
        document.body.style.overflow = '';
        console.log('Modal exited fullscreen');
      } else {
        modal.classList.add('fullscreen');
        document.body.style.overflow = 'hidden';
        console.log('Modal entered fullscreen');
      }
    }
    
    // Event listeners
    document.getElementById('editor-fullscreen-close').addEventListener('click', function() {
      console.log('Editor close button clicked');
      toggleEditorFullscreen();
    });
    
    // Escape key to exit fullscreen
    document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape') {
        const editor = document.getElementById('chart-editor');
        const modal = document.getElementById('view-modal');
        
        if (editor.classList.contains('fullscreen')) {
          toggleEditorFullscreen();
        } else if (modal.classList.contains('fullscreen')) {
          toggleModalFullscreen();
        }
      }
    });
    
    console.log('Dashboard close button demo loaded');
  </script>
</body>
</html>
