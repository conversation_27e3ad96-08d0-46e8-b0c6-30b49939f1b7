<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Dashboard Close Buttons Test</title>
  <!-- Inline CSS to avoid server dependency -->
  <style>
    /* CSS Variables */
    :root {
      --background-color: #1a1a1a;
      --text-color: #ffffff;
      --text-secondary: #cccccc;
      --danger-color: #dc3545;
      --transition-normal: all 0.3s ease;
    }

    /* Fullscreen close button styles (from dashboard CSS) */
    .fullscreen-close-button {
      position: fixed;
      top: 20px;
      left: 20px;
      z-index: 10001;
      background: none;
      border: none;
      color: var(--text-secondary);
      font-size: 2rem;
      cursor: pointer;
      padding: 0;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      transition: var(--transition-normal);
      backdrop-filter: blur(15px);
      background: rgba(0, 0, 0, 0.7);
    }

    .fullscreen-close-button:hover {
      background: var(--danger-color);
      color: white;
      transform: scale(1.1);
    }

    /* Show fullscreen close button only in fullscreen mode */
    .mock-editor.fullscreen .fullscreen-close-button {
      display: flex !important;
    }

    /* Test styles to demonstrate the functionality */
    body {
      margin: 0;
      padding: 20px;
      font-family: Arial, sans-serif;
      background: var(--background-color, #1a1a1a);
      color: var(--text-color, #ffffff);
    }
    
    .test-section {
      margin-bottom: 40px;
      padding: 20px;
      border: 1px solid #333;
      border-radius: 8px;
    }
    
    .test-button {
      background: #007acc;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      margin: 10px;
    }
    
    .test-button:hover {
      background: #005a9e;
    }
    
    /* Mock editor for testing */
    .mock-editor {
      width: 100%;
      height: 400px;
      background: #2d2d2d;
      border: 1px solid #444;
      border-radius: 4px;
      position: relative;
      display: none;
    }
    
    .mock-editor.fullscreen {
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      z-index: 9999;
      background: #1a1a1a;
    }
    
    /* Mock modal for testing */
    .mock-modal {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      display: none;
      align-items: center;
      justify-content: center;
      z-index: 1000;
    }
    
    .mock-modal.fullscreen {
      background: #1a1a1a;
    }
    
    .mock-modal-content {
      background: #2d2d2d;
      border-radius: 8px;
      padding: 20px;
      width: 80%;
      max-width: 600px;
      position: relative;
    }
    
    .mock-modal.fullscreen .mock-modal-content {
      width: 100%;
      height: 100%;
      max-width: none;
      border-radius: 0;
    }
    
    .mock-modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid #444;
    }
  </style>
</head>
<body>
  <h1>Dashboard Close Buttons Test</h1>
  
  <div class="test-section">
    <h2>1. Edit > Full Screen Mode</h2>
    <p>Test the close button functionality for the dashboard editor in fullscreen mode.</p>
    <button class="test-button" onclick="showMockEditor()">Open Editor (Auto Fullscreen)</button>
    <button class="test-button" onclick="toggleEditorFullscreen()">Toggle Editor Fullscreen</button>
    
    <div id="mock-editor" class="mock-editor">
      <!-- Fullscreen close button (only visible in fullscreen mode) -->
      <button id="editor-fullscreen-close" class="fullscreen-close-button" style="display: none;" title="Exit Fullscreen">&times;</button>
      
      <div style="padding: 20px;">
        <h3>Mock Dashboard Editor</h3>
        <p>This simulates the dashboard editor interface.</p>
        <p>When in fullscreen mode, you should see a close button in the top-left corner.</p>
        <button class="test-button" onclick="hideMockEditor()">Close Editor</button>
      </div>
    </div>
  </div>
  
  <div class="test-section">
    <h2>2. View > Full Screen Mode</h2>
    <p>Test the close button functionality for the dashboard view modal in fullscreen mode.</p>
    <button class="test-button" onclick="showMockModal()">Open View Modal</button>
    <button class="test-button" onclick="toggleModalFullscreen()">Toggle Modal Fullscreen</button>
    
    <div id="mock-modal" class="mock-modal">
      <div class="mock-modal-content">
        <div class="mock-modal-header">
          <h3>Mock View Modal</h3>
          <span class="close-modal" onclick="hideMockModal()">&times;</span>
        </div>
        <div>
          <p>This simulates the dashboard view modal.</p>
          <p>When in fullscreen mode, the close button should be repositioned to the top-left corner with enhanced styling.</p>
          <button class="test-button" onclick="hideMockModal()">Close Modal</button>
        </div>
      </div>
    </div>
  </div>
  
  <div class="test-section">
    <h2>Expected Behavior</h2>
    <ul>
      <li><strong>Editor Fullscreen:</strong> Close button appears in top-left corner when entering fullscreen</li>
      <li><strong>View Modal Fullscreen:</strong> Existing close button gets enhanced styling and repositioning</li>
      <li><strong>Gallery Pattern:</strong> Both close buttons follow the same visual style as the gallery</li>
      <li><strong>Hover Effects:</strong> Close buttons should have hover effects (red background, scale transform)</li>
      <li><strong>Positioning:</strong> Close buttons should be positioned at top-left with proper z-index</li>
    </ul>
  </div>

  <script>
    // Mock editor functionality
    function showMockEditor() {
      const editor = document.getElementById('mock-editor');
      const closeButton = document.getElementById('editor-fullscreen-close');
      
      editor.style.display = 'block';
      // Auto-enter fullscreen (like the real dashboard)
      editor.classList.add('fullscreen');
      closeButton.style.display = 'flex';
      document.body.style.overflow = 'hidden';
    }
    
    function hideMockEditor() {
      const editor = document.getElementById('mock-editor');
      const closeButton = document.getElementById('editor-fullscreen-close');
      
      editor.style.display = 'none';
      editor.classList.remove('fullscreen');
      closeButton.style.display = 'none';
      document.body.style.overflow = '';
    }
    
    function toggleEditorFullscreen() {
      const editor = document.getElementById('mock-editor');
      const closeButton = document.getElementById('editor-fullscreen-close');
      
      if (editor.classList.contains('fullscreen')) {
        editor.classList.remove('fullscreen');
        closeButton.style.display = 'none';
        document.body.style.overflow = '';
      } else {
        editor.classList.add('fullscreen');
        closeButton.style.display = 'flex';
        document.body.style.overflow = 'hidden';
      }
    }
    
    // Mock modal functionality
    function showMockModal() {
      const modal = document.getElementById('mock-modal');
      modal.style.display = 'flex';
    }
    
    function hideMockModal() {
      const modal = document.getElementById('mock-modal');
      modal.style.display = 'none';
      modal.classList.remove('fullscreen');
      document.body.style.overflow = '';
    }
    
    function toggleModalFullscreen() {
      const modal = document.getElementById('mock-modal');
      
      if (modal.classList.contains('fullscreen')) {
        modal.classList.remove('fullscreen');
        document.body.style.overflow = '';
      } else {
        modal.classList.add('fullscreen');
        document.body.style.overflow = 'hidden';
      }
    }
    
    // Event listeners for close buttons
    document.getElementById('editor-fullscreen-close').addEventListener('click', function() {
      toggleEditorFullscreen();
    });
    
    // Escape key to exit fullscreen
    document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape') {
        const editor = document.getElementById('mock-editor');
        const modal = document.getElementById('mock-modal');
        
        if (editor.classList.contains('fullscreen')) {
          toggleEditorFullscreen();
        } else if (modal.classList.contains('fullscreen')) {
          toggleModalFullscreen();
        }
      }
    });
  </script>
</body>
</html>
