<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Dashboard - mermantic</title>
  <link rel="stylesheet" href="/css/styles.css">
  <link rel="stylesheet" href="/nui/nui.css">
</head>
<body>
  <!-- Global Navigation Container -->
  <div id="navigation-container"></div>

  <main class="container">
    <section class="dashboard-header">
      <div class="dashboard-title-section">
        <h2>My Diagrams</h2>
        <div class="global-theme-selector">
          <label for="global-theme-selector">Diagram Theme:</label>
          <select id="global-theme-selector" class="nui-select small">
            <option value="default">Default</option>
            <option value="dark">Dark</option>
            <option value="forest">Forest</option>
            <option value="base">Base</option>
            <option value="neutral">Neutral</option>
          </select>
        </div>
      </div>
      <button id="create-new-chart" class="nui-button primary">Create New Diagram</button>
    </section>

    <!-- View Chart Modal -->
    <div id="view-chart-modal" class="modal" style="display: none;">
      <div class="modal-content">
        <div class="modal-header">
          <h3 id="view-chart-title">View Diagram</h3>
          <span class="close-modal" id="close-view-modal">&times;</span>
        </div>
        <div class="modal-body">
          <div class="chart-preview-container">
            <div class="modal-zoom-controls">
              <button type="button" class="zoom-btn" id="modal-zoom-out" title="Zoom Out">−</button>
              <button type="button" class="zoom-btn" id="modal-zoom-reset" title="Reset Zoom">⌂</button>
              <button type="button" class="zoom-btn" id="modal-zoom-in" title="Zoom In">+</button>
              <button type="button" class="zoom-btn" id="modal-fullscreen-toggle" title="Toggle Fullscreen">⛶</button>
            </div>
            <div id="view-chart-container" class="view-chart-container"></div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" id="view-edit-chart" class="nui-button small">Edit</button>
          <button type="button" id="view-delete-chart" class="nui-button small">Delete</button>
          <button type="button" id="view-share-chart" class="nui-button small">Share</button>
          <div class="download-group">
            <button type="button" id="download-png" class="nui-button small download-btn">📥 PNG</button>
            <button type="button" id="download-svg" class="nui-button small download-btn">📥 SVG</button>
          </div>
          <button type="button" id="close-view-button" class="nui-button secondary small">Close</button>
        </div>
      </div>
    </div>

    <section id="chart-editor" class="chart-editor ide-layout" style="display: none;">
      <form id="chart-form">
        <input type="hidden" id="chart-id">

        <!-- IDE Toolbar -->
        <div class="ide-toolbar">
          <div class="toolbar-left">
            <h3 id="editor-title">Create New Diagram</h3>
          </div>
          <div class="toolbar-center">
            <div class="toolbar-actions">
              <button type="button" id="toggle-properties" class="toolbar-btn" title="Toggle Properties Panel">
                <span class="icon">⚙️</span> Properties
              </button>
              <button type="button" id="toggle-help" class="toolbar-btn" title="Show/Hide Help">
                <span class="icon">❓</span> Help
              </button>
            </div>
          </div>
          <div class="toolbar-right">
            <div class="toolbar-actions">
              <button type="submit" class="nui-button primary small">💾 Save</button>
              <button type="button" id="toggle-fullscreen" class="nui-button secondary small">⛶ Fullscreen</button>
              <button type="button" id="cancel-edit" class="nui-button secondary small">✕ Cancel</button>
            </div>
          </div>
        </div>

        <!-- IDE Main Content -->
        <div class="ide-content">
          <!-- Properties Panel -->
          <div id="properties-panel" class="properties-panel">
            <div class="properties-header">
              <h4>Properties</h4>
              <button type="button" id="close-properties" class="close-btn" title="Close Properties Panel">✕</button>
            </div>
            <div class="properties-content">
              <div class="form-group">
                <label for="chart-title">Title</label>
                <input type="text" id="chart-title" name="chart-title" class="nui-input" required>
              </div>

              <div class="form-group">
                <label for="chart-folder">Folder</label>
                <div class="folder-input-container">
                  <input type="text" id="chart-folder" name="chart-folder" class="nui-input" placeholder="Enter folder name or leave empty">
                  <select id="folder-suggestions" class="folder-suggestions nui-select">
                    <option value="">Select existing folder...</option>
                  </select>
                </div>
              </div>

              <div class="form-group">
                <label for="chart-notes">Notes</label>
                <textarea id="chart-notes" name="chart-notes" class="nui-textarea" placeholder="Add notes or description for this diagram..." rows="3"></textarea>
              </div>

              <div class="form-group">
                <label for="chart-theme">Diagram Theme</label>
                <select id="chart-theme" name="chart-theme" class="nui-select">
                  <option value="default">Default</option>
                  <option value="dark">Dark</option>
                  <option value="forest">Forest</option>
                  <option value="base">Base</option>
                  <option value="neutral">Neutral</option>
                </select>
                <small class="form-help">Choose the visual theme for your diagram</small>
              </div>

              <div class="form-group">
                <label for="chart-public">
                  <input type="checkbox" id="chart-public" name="chart-public">
                  Make diagram public
                </label>
              </div>
            </div>
          </div>

          <!-- Editor Container -->
          <div class="editor-container">
            <div class="editor-panel">
              <div class="editor-header">
                <h4>Mermaid Syntax <span id="syntax-indicator" class="syntax-indicator"></span></h4>
              </div>
              <textarea id="chart-content" class="mermaid-editor" required>graph TD
    A[Start] --> B{Is it working?}
    B -->|Yes| C[Great!]
    B -->|No| D[Debug]
    D --> B</textarea>

              <div id="help-panel" class="help-panel" style="display: none;">
                <h5>Quick Reference</h5>
                <div class="help-examples">
                  <button type="button" class="help-example" data-example="flowchart">Flowchart</button>
                  <button type="button" class="help-example" data-example="sequence">Sequence</button>
                  <button type="button" class="help-example" data-example="gantt">Gantt</button>
                  <button type="button" class="help-example" data-example="pie">Pie Chart</button>
                </div>
                <div class="help-shortcuts">
                  <strong>Shortcuts:</strong><br>
                  <kbd>Ctrl+S</kbd> Save • <kbd>Esc</kbd> Cancel • <kbd>Ctrl+Enter</kbd> Update Preview • <kbd>F11</kbd> Fullscreen
                </div>
                <div class="help-unicode">
                  <strong>Unicode Characters:</strong><br>
                  Some Unicode characters (emojis, special symbols) may be automatically replaced with compatible alternatives for better rendering.
                </div>
              </div>
            </div>

            <div class="preview-panel">
              <div class="preview-header">
                <h4>Preview</h4>
                <div class="preview-controls">
                  <div class="preview-zoom-controls" id="preview-zoom-controls" style="display: flex;">
                    <button type="button" class="zoom-btn" id="preview-zoom-out" title="Zoom Out">−</button>
                    <button type="button" class="zoom-btn" id="preview-zoom-reset" title="Reset Zoom">⌂</button>
                    <button type="button" class="zoom-btn" id="preview-zoom-in" title="Zoom In">+</button>
                  </div>
                  <div class="preview-download-group">
                    <button type="button" id="preview-download-png" class="nui-button small download-btn" title="Download as PNG">📥 PNG</button>
                    <button type="button" id="preview-download-svg" class="nui-button small download-btn" title="Download as SVG">📥 SVG</button>
                  </div>
                </div>
              </div>
              <div id="chart-preview" class="mermaid-preview chart-zoomable"></div>
            </div>
          </div>
        </div>
      </form>
    </section>

    <section id="charts-list" class="charts-list">
      <div id="no-charts-message" style="display: none;">
        <p>You don't have any diagrams yet. Create your first one!</p>
      </div>

      <div id="charts-grid" class="charts-grid">
        <!-- Charts will be loaded here dynamically -->
      </div>
    </section>
  </main>

  <!-- Global Footer Container -->
  <div id="footer-container"></div>

  <!-- Component Scripts -->
  <script src="/js/components/navigation.js"></script>
  <script src="/js/components/footer.js"></script>
  <script src="/js/components/analytics-manager.js"></script>
  <script src="/js/components/seo-manager.js"></script>
  <script src="/js/components/component-loader.js"></script>

  <script src="/js/mermaid.min.js"></script>
  <script src="/nui/nui.js"></script>
  <script src="/js/auth.js"></script>
  <script src="/js/charts.js"></script>
  <script src="/js/download-utils.js"></script>
  <script>
    // Theme management
    let currentMermaidTheme = localStorage.getItem('mermaidTheme') || 'dark'; // Default to dark theme for our dark UI

    // Initialize Mermaid with better error handling and dynamic theme
    function initializeMermaid(theme = currentMermaidTheme) {
      mermaid.initialize({
        startOnLoad: false,
        securityLevel: 'loose',
        theme: theme,
        logLevel: 'fatal', // Reduce console noise
        fontFamily: 'monospace',
        flowchart: {
          useMaxWidth: true,
          htmlLabels: true,
          curve: 'linear' // Use linear curves which are more reliable
        },
        er: {
          useMaxWidth: true
        },
        sequence: {
          useMaxWidth: true,
          wrap: true,
          showSequenceNumbers: false
        },
        gantt: {
          useMaxWidth: true
        },
        journey: {
          useMaxWidth: true
        },
        // Suppress errors to reduce console noise
        suppressErrors: true
      });
    }

    // Initialize with current theme
    initializeMermaid();

    // Function to change mermaid theme
    function changeMermaidTheme(newTheme) {
      currentMermaidTheme = newTheme;
      localStorage.setItem('mermaidTheme', newTheme);

      // Re-initialize mermaid with new theme
      initializeMermaid(newTheme);

      // Update the theme selectors if they exist
      const themeSelector = document.getElementById('chart-theme');
      if (themeSelector) {
        themeSelector.value = newTheme;
      }

      const globalThemeSelector = document.getElementById('global-theme-selector');
      if (globalThemeSelector) {
        globalThemeSelector.value = newTheme;
      }

      // Re-render current preview if editor is open
      if (document.getElementById('chart-editor').style.display === 'block') {
        updateChartPreview();
      }

      // Re-render any visible charts in the grid
      reRenderVisibleCharts();
    }

    // Function to re-render all visible charts with new theme
    function reRenderVisibleCharts() {
      const chartPreviews = document.querySelectorAll('.chart-preview-small .mermaid');
      chartPreviews.forEach((mermaidDiv, index) => {
        if (mermaidDiv.textContent) {
          // Clear the current content
          mermaidDiv.innerHTML = '';

          // Re-render with new theme after a short delay
          setTimeout(() => {
            try {
              mermaid.init(undefined, mermaidDiv);
            } catch (error) {
              console.error('Error re-rendering chart preview:', error);
            }
          }, index * 50); // Stagger the re-renders
        }
      });
    }

    document.addEventListener('DOMContentLoaded', function() {
      // Check if user is logged in via localStorage
      let user = JSON.parse(localStorage.getItem('user'));

      // If no user in localStorage, check if we're coming from Google auth
      if (!user) {
        console.log('No user in localStorage, checking session...');
        // Fetch current user from session
        fetchCurrentUser()
          .then(sessionUser => {
            if (sessionUser) {
              // Store user in localStorage
              localStorage.setItem('user', JSON.stringify(sessionUser));
              user = sessionUser;
              console.log('User found in session, stored in localStorage:', user);
              // Load user's charts
              loadUserCharts();
            } else {
              // No user in session either, redirect to login
              window.location.href = '/login.html';
            }
          })
          .catch(error => {
            console.error('Error fetching current user:', error);
            window.location.href = '/login.html';
          });
      } else {
        // User already in localStorage, but verify session is still valid
        verifySession()
          .then(() => {
            // Session is valid, load charts
            loadUserCharts();
          })
          .catch(error => {
            console.error('Error verifying session:', error);
            // Try to load charts anyway
            loadUserCharts();
          });
      }

      // Setup event listeners
      document.getElementById('create-new-chart').addEventListener('click', function() {
        showChartEditor(true);
      });
      document.getElementById('cancel-edit').addEventListener('click', hideChartEditor);

      // Fullscreen toggle
      document.getElementById('toggle-fullscreen').addEventListener('click', function() {
        const editor = document.getElementById('chart-editor');
        const button = this;
        const zoomControls = document.getElementById('preview-zoom-controls');

        if (editor.classList.contains('fullscreen')) {
          editor.classList.remove('fullscreen');
          button.textContent = '⛶ Fullscreen';
          document.body.style.overflow = '';
          // Keep zoom controls visible in normal mode too
          if (zoomControls) zoomControls.style.display = 'flex';
        } else {
          editor.classList.add('fullscreen');
          button.textContent = '⛶ Exit Fullscreen';
          document.body.style.overflow = 'hidden';
          // Show zoom controls in fullscreen mode
          if (zoomControls) zoomControls.style.display = 'flex';
        }
      });

      // Add debounced input listener for preview updates and auto-save
      let previewUpdateTimeout;
      let autoSaveTimeout;
      document.getElementById('chart-content').addEventListener('input', function() {
        clearTimeout(previewUpdateTimeout);
        clearTimeout(autoSaveTimeout);
        previewUpdateTimeout = setTimeout(updateChartPreview, 500); // 500ms debounce
        autoSaveTimeout = setTimeout(autoSaveDraft, 2000); // Auto-save after 2 seconds
      });

      document.getElementById('chart-form').addEventListener('submit', saveChart);

      // Folder suggestions functionality
      document.getElementById('folder-suggestions').addEventListener('change', function() {
        if (this.value) {
          document.getElementById('chart-folder').value = this.value;
        }
      });

      // Theme selector functionality
      document.getElementById('chart-theme').addEventListener('change', function() {
        const newTheme = this.value;
        changeMermaidTheme(newTheme);
      });

      // Set initial theme in selectors
      document.getElementById('chart-theme').value = currentMermaidTheme;
      document.getElementById('global-theme-selector').value = currentMermaidTheme;

      // Global theme selector functionality
      document.getElementById('global-theme-selector').addEventListener('change', function() {
        const newTheme = this.value;
        changeMermaidTheme(newTheme);
      });

      // Load folders when showing editor
      loadFolders();

      // Properties panel functionality
      document.getElementById('toggle-properties').addEventListener('click', function() {
        const propertiesPanel = document.getElementById('properties-panel');
        propertiesPanel.classList.toggle('hidden');
      });

      document.getElementById('close-properties').addEventListener('click', function() {
        const propertiesPanel = document.getElementById('properties-panel');
        propertiesPanel.classList.add('hidden');
      });

      // Help panel functionality
      document.getElementById('toggle-help').addEventListener('click', function() {
        const helpPanel = document.getElementById('help-panel');
        helpPanel.style.display = helpPanel.style.display === 'none' ? 'block' : 'none';
      });

      // Example templates
      const examples = {
        flowchart: `graph TD
    A[Start] --> B{Decision}
    B -->|Yes| C[Action 1]
    B -->|No| D[Action 2]
    C --> E[End]
    D --> E`,
        sequence: `sequenceDiagram
    participant A as Alice
    participant B as Bob
    A->>B: Hello Bob!
    B-->>A: Hello Alice!
    A->>B: How are you?
    B-->>A: I'm good, thanks!`,
        gantt: `gantt
    title Project Timeline
    dateFormat YYYY-MM-DD
    section Planning
    Research    :done, research, 2024-01-01, 2024-01-07
    Design      :active, design, 2024-01-08, 2024-01-15
    section Development
    Coding      :coding, after design, 2024-01-30
    Testing     :testing, after coding, 2024-02-15`,
        pie: `pie title Favorite Programming Languages
    "JavaScript" : 35
    "Python" : 25
    "Java" : 20
    "C++" : 15
    "Other" : 5`
      };

      // Add event listeners to example buttons
      document.querySelectorAll('.help-example').forEach(button => {
        button.addEventListener('click', function() {
          const exampleType = this.getAttribute('data-example');
          const exampleCode = examples[exampleType];
          if (exampleCode) {
            document.getElementById('chart-content').value = exampleCode;
            updateChartPreview();
          }
        });
      });

      // Setup modal event listeners
      document.getElementById('close-view-modal').addEventListener('click', hideViewModal);
      document.getElementById('close-view-button').addEventListener('click', hideViewModal);
      document.getElementById('view-edit-chart').addEventListener('click', function() {
        const chartId = this.getAttribute('data-id');
        hideViewModal();
        editChart(chartId);
      });
      document.getElementById('view-delete-chart').addEventListener('click', function() {
        const chartId = this.getAttribute('data-id');
        hideViewModal();
        deleteChart(chartId);
      });
      document.getElementById('view-share-chart').addEventListener('click', function() {
        const shareId = this.getAttribute('data-id');
        shareChart(shareId);
      });
      // Modal zoom controls
      document.getElementById('modal-zoom-in').addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        modalZoomIn();
      });
      document.getElementById('modal-zoom-out').addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        modalZoomOut();
      });
      document.getElementById('modal-zoom-reset').addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        modalZoomReset();
      });
      document.getElementById('modal-fullscreen-toggle').addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        toggleViewFullscreen();
      });
      document.getElementById('download-png').addEventListener('click', function() {
        downloadChartAsImage('png', 'view-chart-container');
      });
      document.getElementById('download-svg').addEventListener('click', function() {
        downloadChartAsImage('svg', 'view-chart-container');
      });
      document.getElementById('preview-download-png').addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        downloadChartAsImage('png', 'chart-preview');
      });
      document.getElementById('preview-download-svg').addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        downloadChartAsImage('svg', 'chart-preview');
      });

      // Preview zoom controls
      document.getElementById('preview-zoom-in').addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        zoomPreview(1.2);
      });
      document.getElementById('preview-zoom-out').addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        zoomPreview(0.8);
      });
      document.getElementById('preview-zoom-reset').addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        resetPreviewZoom();
      });

      // Click outside modal in fullscreen to exit fullscreen (but not close modal)
      document.getElementById('view-chart-modal').addEventListener('click', function(e) {
        if (e.target === this && this.classList.contains('fullscreen')) {
          toggleViewFullscreen();
        }
      });

      // Keyboard shortcuts
      document.addEventListener('keydown', function(e) {
        // Check if editor is visible
        const editorVisible = document.getElementById('chart-editor').style.display === 'flex';
        // Check if view modal is visible
        const viewModalVisible = document.getElementById('view-chart-modal').style.display === 'block';

        if (editorVisible) {
          // Editor shortcuts
          // Ctrl+S or Cmd+S to save
          if ((e.ctrlKey || e.metaKey) && e.key === 's') {
            e.preventDefault();
            document.getElementById('chart-form').dispatchEvent(new Event('submit'));
          }
          // Escape to cancel
          else if (e.key === 'Escape') {
            e.preventDefault();
            hideChartEditor();
          }
          // Ctrl+Enter or Cmd+Enter to update preview
          else if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            e.preventDefault();
            updateChartPreview();
          }
          // F11 for fullscreen
          else if (e.key === 'F11') {
            e.preventDefault();
            document.getElementById('toggle-fullscreen').click();
          }
        } else if (viewModalVisible) {
          // View modal shortcuts
          // Escape to close modal
          if (e.key === 'Escape') {
            e.preventDefault();
            hideViewModal();
          }
          // F11 for fullscreen
          else if (e.key === 'F11') {
            e.preventDefault();
            toggleViewFullscreen();
          }
        }
      });

      // Check for template usage from gallery
      checkForTemplate();

      // Initial preview update - only if editor is visible
      setTimeout(() => {
        const editor = document.getElementById('chart-editor');
        if (editor && editor.style.display === 'flex') {
          updateChartPreview();
        }
      }, 100);
    });

    // Check for template usage from gallery
    function checkForTemplate() {
      const urlParams = new URLSearchParams(window.location.search);
      const isTemplate = urlParams.get('template');

      if (isTemplate === 'true') {
        const templateCode = localStorage.getItem('templateCode');
        const templateTitle = localStorage.getItem('templateTitle');

        if (templateCode) {
          // Show the editor with template data
          showChartEditor(true);

          // Fill in the template data
          document.getElementById('chart-content').value = templateCode;
          if (templateTitle) {
            document.getElementById('chart-title').value = `${templateTitle} (Copy)`;
          }

          // Update preview
          updateChartPreview();

          // Clean up localStorage
          localStorage.removeItem('templateCode');
          localStorage.removeItem('templateTitle');

          // Clean up URL
          window.history.replaceState({}, document.title, window.location.pathname);

          // Show a notification
          showTemplateNotification();
        }
      }
    }

    // Show template notification
    function showTemplateNotification() {
      const notification = document.createElement('div');
      notification.className = 'template-notification';
      notification.innerHTML = `
        <div class="notification-content">
          <span class="notification-icon">🎨</span>
          <span class="notification-text">Template loaded! You can now customize and save your diagram.</span>
          <button class="notification-close" onclick="this.parentElement.parentElement.remove()">&times;</button>
        </div>
      `;

      notification.style.cssText = `
        position: fixed;
        top: 80px;
        right: 20px;
        background: var(--surface-elevated);
        border: 1px solid var(--primary-color);
        border-radius: var(--border-radius);
        padding: 1rem;
        box-shadow: var(--box-shadow-glow);
        z-index: 1000;
        animation: slideInRight 0.3s ease;
        max-width: 350px;
      `;

      document.body.appendChild(notification);

      // Auto-remove after 5 seconds
      setTimeout(() => {
        if (notification.parentNode) {
          notification.style.animation = 'slideOutRight 0.3s ease';
          setTimeout(() => {
            if (notification.parentNode) {
              notification.parentNode.removeChild(notification);
            }
          }, 300);
        }
      }, 5000);
    }

    // Preview zoom functionality
    let previewZoom = 1;

    function zoomPreview(factor) {
      previewZoom *= factor;
      previewZoom = Math.max(0.5, Math.min(3, previewZoom)); // Clamp between 0.5x and 3x
      applyPreviewZoom();
    }

    function resetPreviewZoom() {
      previewZoom = 1;
      applyPreviewZoom();
    }

    function applyPreviewZoom() {
      const preview = document.getElementById('chart-preview');
      const mermaidElement = preview.querySelector('.mermaid, svg');

      if (mermaidElement) {
        mermaidElement.style.transform = `scale(${previewZoom})`;
        mermaidElement.style.transformOrigin = 'top left'; // Better for scrolling
        mermaidElement.style.transition = 'transform 0.3s ease';

        // Ensure the preview container can scroll when zoomed
        if (previewZoom > 1) {
          preview.style.overflow = 'auto';
          mermaidElement.style.cursor = 'grab';
        } else {
          preview.style.overflow = 'auto';
          mermaidElement.style.cursor = 'default';
        }
      }

      // Update button states
      const zoomInBtn = document.getElementById('preview-zoom-in');
      const zoomOutBtn = document.getElementById('preview-zoom-out');

      if (zoomInBtn) zoomInBtn.disabled = previewZoom >= 3;
      if (zoomOutBtn) zoomOutBtn.disabled = previewZoom <= 0.5;
    }

    function showChartEditor(isNew = true) {
      document.getElementById('charts-list').style.display = 'none';
      const editor = document.getElementById('chart-editor');
      editor.style.display = 'flex';

      if (isNew) {
        document.getElementById('editor-title').textContent = 'Create New Diagram';
        document.getElementById('chart-form').reset();
        document.getElementById('chart-id').value = '';
        // Show properties panel for new diagrams
        document.getElementById('properties-panel').classList.remove('hidden');
      } else {
        // For editing existing diagrams, keep properties panel state
      }

      // Reset preview zoom
      resetPreviewZoom();

      // Automatically enter fullscreen mode for better IDE experience
      editor.classList.add('fullscreen');
      document.getElementById('toggle-fullscreen').textContent = '⛶ Exit Fullscreen';
      document.body.style.overflow = 'hidden';

      // Ensure zoom controls are visible
      const zoomControls = document.getElementById('preview-zoom-controls');
      if (zoomControls) zoomControls.style.display = 'flex';

      // Update the preview with current content
      updateChartPreview();
    }

    function hideChartEditor() {
      const editor = document.getElementById('chart-editor');
      editor.style.display = 'none';
      document.getElementById('charts-list').style.display = 'block';

      // Exit fullscreen mode when closing editor
      editor.classList.remove('fullscreen');
      document.getElementById('toggle-fullscreen').textContent = '⛶ Fullscreen';
      document.body.style.overflow = '';

      // Clean up any zoom controls when hiding the editor
      cleanupZoomControls();
    }

    // Centralized function to clean up dynamically created zoom controls
    function cleanupZoomControls() {
      // Remove only dynamically created zoom controls (not the built-in preview-zoom-controls)
      // Remove zoom controls from view modal area
      const viewContainer = document.getElementById('view-chart-container');
      if (viewContainer && viewContainer.parentNode) {
        const existingControls = viewContainer.parentNode.querySelectorAll('.zoom-controls');
        existingControls.forEach(control => control.remove());
      }

      // Remove any orphaned dynamically created zoom controls (but preserve preview-zoom-controls)
      document.querySelectorAll('.zoom-controls').forEach(control => {
        // Only remove if it's not the built-in preview controls
        if (!control.id || control.id !== 'preview-zoom-controls') {
          control.remove();
        }
      });
    }

    // Modal zoom functionality (gallery-style built-in controls)
    let modalCurrentZoom = 1.0;
    const modalMinZoom = 0.5;
    const modalMaxZoom = 3.0;
    const modalZoomStep = 0.25;

    function updateModalZoom() {
      const mermaidElement = document.querySelector('#view-chart-container .mermaid');
      if (mermaidElement) {
        mermaidElement.style.transform = `scale(${modalCurrentZoom})`;
        mermaidElement.style.transformOrigin = 'center center';

        // Update button states
        const zoomInBtn = document.getElementById('modal-zoom-in');
        const zoomOutBtn = document.getElementById('modal-zoom-out');

        if (zoomInBtn) zoomInBtn.disabled = modalCurrentZoom >= modalMaxZoom;
        if (zoomOutBtn) zoomOutBtn.disabled = modalCurrentZoom <= modalMinZoom;
      }
    }

    function modalZoomIn() {
      if (modalCurrentZoom < modalMaxZoom) {
        modalCurrentZoom += modalZoomStep;
        updateModalZoom();
      }
    }

    function modalZoomOut() {
      if (modalCurrentZoom > modalMinZoom) {
        modalCurrentZoom -= modalZoomStep;
        updateModalZoom();
      }
    }

    function modalZoomReset() {
      modalCurrentZoom = 1.0;
      updateModalZoom();
    }

    // Enhanced error handler for Mermaid rendering issues
    function handleMermaidRenderError(error, preview, content, mermaidDiv, id) {
      console.error('Handling Mermaid render error:', error);

      // Check if it's the specific getBoundingClientRect error and retry if possible
      if (error.message && error.message.includes('getBoundingClientRect')) {
        if (previewRetryCount < maxPreviewRetries) {
          console.warn(`getBoundingClientRect error detected, retrying (attempt ${previewRetryCount + 1}/${maxPreviewRetries})...`);
          setTimeout(() => {
            isUpdatingPreview = false;
            updateChartPreview(previewRetryCount + 1);
          }, 500 * (previewRetryCount + 1)); // Longer exponential backoff
          return;
        } else {
          console.error('Max retries reached for getBoundingClientRect error');
        }
      }

      // Show appropriate error message
      const errorMessage = error.message || error.toString();
      preview.innerHTML = `<div class="error-message">
        <strong>Mermaid Rendering Error:</strong><br>
        ${errorMessage}<br><br>
        <strong>Common issues to check:</strong><br>
        - Indentation and whitespace<br>
        - Special characters (try using simple ASCII characters)<br>
        - Missing connections or closing brackets<br>
        - Subgraph syntax (ensure proper nesting)<br>
        ${previewRetryCount >= maxPreviewRetries ? '<br><strong>Note:</strong> Multiple retry attempts failed. Try refreshing the page.' : ''}
      </div>`;

      isUpdatingPreview = false;
    }

    // Enhanced Mermaid rendering for modal view
    function renderMermaidInModal(container, mermaidDiv, content, retryCount = 0) {
      const maxRetries = 3;

      // Ensure Mermaid is properly loaded
      if (!window.mermaid || !mermaid.mermaidAPI) {
        console.error('Mermaid library not properly loaded');
        container.innerHTML = `<div class="error-message">
          <strong>Mermaid Library Error:</strong><br>
          The Mermaid library is not properly loaded. Please refresh the page.
        </div>`;
        return;
      }

      // Ensure the container has proper dimensions
      if (container.offsetWidth === 0 || container.offsetHeight === 0) {
        console.warn('Modal container has no dimensions, waiting...');
        if (retryCount < maxRetries) {
          setTimeout(() => {
            renderMermaidInModal(container, mermaidDiv, content, retryCount + 1);
          }, 200 * (retryCount + 1));
        } else {
          container.innerHTML = `<div class="error-message">
            <strong>Container Error:</strong><br>
            Unable to determine container dimensions. Please try again.
          </div>`;
        }
        return;
      }

      // Create unique ID for this render
      const uniqueId = 'modal-mermaid-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
      mermaidDiv.id = uniqueId;

      // Use requestAnimationFrame to ensure DOM is ready
      requestAnimationFrame(() => {
        try {
          // Try render API first
          const renderPromise = mermaid.mermaidAPI.render(uniqueId + '-svg', content);

          if (renderPromise && typeof renderPromise.then === 'function') {
            // Handle promise-based render
            renderPromise
              .then((result) => {
                if (result && result.svg) {
                  mermaidDiv.innerHTML = result.svg;
                  // Reset zoom and update controls after successful render
                  modalCurrentZoom = 1.0;
                  updateModalZoom();
                } else {
                  throw new Error('No SVG generated from render');
                }
              })
              .catch((renderError) => {
                console.error('Modal render promise error:', renderError);
                handleModalRenderError(renderError, container, mermaidDiv, content, retryCount);
              });
          } else {
            // Handle synchronous result
            if (renderPromise && renderPromise.svg) {
              mermaidDiv.innerHTML = renderPromise.svg;
              // Reset zoom and update controls after successful render
              modalCurrentZoom = 1.0;
              updateModalZoom();
            } else {
              throw new Error('Render API returned no result');
            }
          }
        } catch (renderApiError) {
          console.warn('Modal render API failed, falling back to init method:', renderApiError);

          // Fallback to init method
          try {
            // Ensure element is properly attached
            if (!document.body.contains(mermaidDiv)) {
              throw new Error('Element not in document body');
            }

            // Force layout calculation
            mermaidDiv.offsetHeight; // Force reflow

            // Use init method
            mermaid.init(undefined, mermaidDiv);

            // Check result and update zoom controls
            setTimeout(() => {
              if (mermaidDiv.querySelector('svg')) {
                // Reset zoom and update controls after successful render
                modalCurrentZoom = 1.0;
                updateModalZoom();
              } else {
                container.innerHTML = `<div class="error-message">
                  <strong>Unable to render diagram</strong><br>
                  Please check your Mermaid syntax.
                </div>`;
              }
            }, 150);

          } catch (initError) {
            console.error('Modal init method also failed:', initError);
            handleModalRenderError(initError, container, mermaidDiv, content, retryCount);
          }
        }
      });
    }

    // Error handler for modal Mermaid rendering
    function handleModalRenderError(error, container, mermaidDiv, content, retryCount) {
      const maxRetries = 3;

      // Check for getBoundingClientRect error and retry
      if (error.message && error.message.includes('getBoundingClientRect')) {
        if (retryCount < maxRetries) {
          console.warn(`Modal getBoundingClientRect error, retrying (attempt ${retryCount + 1}/${maxRetries})...`);
          setTimeout(() => {
            renderMermaidInModal(container, mermaidDiv, content, retryCount + 1);
          }, 500 * (retryCount + 1));
          return;
        } else {
          console.error('Max retries reached for modal getBoundingClientRect error');
        }
      }

      // Show error message
      const errorMessage = error.message || error.toString();
      container.innerHTML = `<div class="error-message">
        <strong>Modal Rendering Error:</strong><br>
        ${errorMessage}<br><br>
        <strong>Common issues to check:</strong><br>
        - Indentation and whitespace<br>
        - Special characters (try using simple ASCII characters)<br>
        - Missing connections or closing brackets<br>
        - Subgraph syntax (ensure proper nesting)<br>
        ${retryCount >= maxRetries ? '<br><strong>Note:</strong> Multiple retry attempts failed. Try refreshing the page.' : ''}
      </div>`;
    }

    // Global variable to prevent overlapping preview updates
    let isUpdatingPreview = false;
    let previewRetryCount = 0;
    const maxPreviewRetries = 3;

    // Function to sanitize Mermaid content for problematic Unicode characters
    function sanitizeMermaidContent(content) {
      if (!content) return {
        content: content,
        hasReplacements: false,
        originalContent: content
      };

      // Map of specific problematic Unicode characters to safe alternatives
      const unicodeReplacements = {
        '➕': '+',     // ➕ Heavy Plus Sign
        '➖': '-',     // ➖ Heavy Minus Sign
        '✓': 'check', // ✓ Check Mark
        '✗': 'x',     // ✗ Ballot X
        '⚠': 'warn',  // ⚠ Warning Sign
        '⭐': 'star',  // ⭐ White Medium Star
        '🔥': 'fire',   // 🔥 Fire
        '💡': 'idea',   // 💡 Electric Light Bulb
        '📊': 'chart',  // 📊 Bar Chart
        '📈': 'up',     // 📈 Chart Increasing
        '📉': 'down',   // 📉 Chart Decreasing
        '🎯': 'target', // 🎯 Direct Hit
        '⚡': 'fast',  // ⚡ High Voltage
        '🔒': 'lock',   // 🔒 Lock
        '🔓': 'unlock', // 🔓 Open Lock
        '❌': 'x',     // ❌ Cross Mark
        '⭕': 'o',     // ⭕ Heavy Large Circle
        '🟢': 'green',  // 🟢 Green Circle
        '🔴': 'red',    // 🔴 Red Circle
        '🟡': 'yellow', // 🟡 Yellow Circle
        '🔵': 'blue',   // 🔵 Blue Circle
        '⚫': 'black', // ⚫ Medium Black Circle
        '⚪': 'white'  // ⚪ Medium White Circle
      };

      let sanitized = content;
      let hasReplacements = false;

      // Replace only the specific known problematic Unicode characters
      for (const [unicode, replacement] of Object.entries(unicodeReplacements)) {
        if (sanitized.includes(unicode)) {
          sanitized = sanitized.split(unicode).join(replacement);
          hasReplacements = true;
        }
      }

      // Only replace specific invisible/control characters that break parsing
      const invisibleCharReplacements = [
        [/[\u2000-\u200F]/g, ' '],  // Various spaces and invisible chars
        [/[\u2028-\u2029]/g, ' '],  // Line/paragraph separators
        [/[\uFEFF]/g, ''],          // Byte order mark (remove completely)
        [/[\u00A0]/g, ' ']          // Non-breaking space
      ];

      for (const [regex, replacement] of invisibleCharReplacements) {
        const matches = sanitized.match(regex);
        if (matches && matches.length > 0) {
          sanitized = sanitized.replace(regex, replacement);
          hasReplacements = true;
        }
      }

      return {
        content: sanitized,
        hasReplacements: hasReplacements,
        originalContent: content
      };
    }

    // Function to update syntax indicator
    function updateSyntaxIndicator(status, message = '') {
      const indicator = document.getElementById('syntax-indicator');
      indicator.className = 'syntax-indicator ' + status;

      switch(status) {
        case 'valid':
          indicator.textContent = '✓ Valid';
          indicator.title = 'Syntax is valid';
          break;
        case 'invalid':
          indicator.textContent = '✗ Invalid';
          indicator.title = message || 'Syntax error detected';
          break;
        case 'checking':
          indicator.textContent = '⟳ Checking...';
          indicator.title = 'Validating syntax...';
          break;
        case 'warning':
          indicator.textContent = '⚠ Warning';
          indicator.title = message || 'Potential issues detected';
          break;
        default:
          indicator.textContent = '';
          indicator.title = '';
      }
    }

    function updateChartPreview(retryAttempt = 0) {
      // Prevent overlapping updates
      if (isUpdatingPreview) {
        return;
      }

      isUpdatingPreview = true;
      previewRetryCount = retryAttempt;
      const originalContent = document.getElementById('chart-content').value;
      const preview = document.getElementById('chart-preview');

      // Update syntax indicator
      updateSyntaxIndicator('checking');

      try {
        // Clear the preview div completely
        preview.innerHTML = '';

        // No need to clean up zoom controls in edit view - they're built into the header

        // Skip rendering if content is empty
        if (!originalContent.trim()) {
          updateSyntaxIndicator('');
          isUpdatingPreview = false; // Reset flag
          return;
        }

        // Ensure Mermaid is properly loaded and initialized
        if (!window.mermaid || !mermaid.mermaidAPI) {
          console.error('Mermaid library not properly loaded');
          preview.innerHTML = `<div class="error-message">
            <strong>Mermaid Library Error:</strong><br>
            The Mermaid library is not properly loaded. Please refresh the page.
          </div>`;
          isUpdatingPreview = false;
          return;
        }

        // Sanitize the content for problematic Unicode characters
        const sanitizationResult = sanitizeMermaidContent(originalContent);
        const content = sanitizationResult.content;

        // Quick syntax validation using mermaid.parse
        try {
          mermaid.parse(content);
          if (sanitizationResult.hasReplacements) {
            updateSyntaxIndicator('warning', 'Some Unicode characters were replaced for compatibility');
          } else {
            updateSyntaxIndicator('valid');
          }
        } catch (parseError) {
          updateSyntaxIndicator('invalid', parseError.message || parseError);
          // Continue with rendering to show the error in preview
        }

        // Generate a unique ID for the diagram
        const id = 'mermaid-diagram-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);

        // Create a wrapper div to ensure proper layout
        const wrapperDiv = document.createElement('div');
        wrapperDiv.style.width = '100%';
        wrapperDiv.style.minHeight = '200px';
        wrapperDiv.style.position = 'relative';
        wrapperDiv.style.overflow = 'visible';

        // Create a div element with the mermaid class
        const mermaidDiv = document.createElement('div');
        mermaidDiv.id = id;
        mermaidDiv.className = 'mermaid';
        mermaidDiv.textContent = content;

        // Ensure the div is properly styled and has dimensions
        mermaidDiv.style.visibility = 'hidden'; // Hide until rendered
        mermaidDiv.style.width = '100%';
        mermaidDiv.style.minHeight = '200px';
        mermaidDiv.style.display = 'block';

        // Add mermaid div to wrapper, then wrapper to preview
        wrapperDiv.appendChild(mermaidDiv);
        preview.appendChild(wrapperDiv);

        // Ensure the preview container has proper dimensions before rendering
        if (preview.offsetWidth === 0 || preview.offsetHeight === 0) {
          console.warn('Preview container has no dimensions, waiting...');
          setTimeout(() => {
            isUpdatingPreview = false;
            updateChartPreview();
          }, 100);
          return;
        }

        // Use requestAnimationFrame to ensure DOM is fully rendered
        requestAnimationFrame(() => {
          // Double-check dimensions after DOM update
          if (preview.offsetWidth === 0 || preview.offsetHeight === 0) {
            console.warn('Preview container still has no dimensions after DOM update');
            setTimeout(() => {
              isUpdatingPreview = false;
              if (previewRetryCount < maxPreviewRetries) {
                updateChartPreview(previewRetryCount + 1);
              }
            }, 200);
            return;
          }

          try {
            // Use Mermaid's render API instead of init for better control
            const renderPromise = mermaid.mermaidAPI.render(id + '-svg', content);

            if (renderPromise && typeof renderPromise.then === 'function') {
              // Handle promise-based render
              renderPromise
                .then((result) => {
                  if (result && result.svg) {
                    mermaidDiv.innerHTML = result.svg;
                    mermaidDiv.style.visibility = 'visible';
                    previewRetryCount = 0; // Reset retry count on successful render
                    isUpdatingPreview = false;
                  } else {
                    throw new Error('No SVG generated from render');
                  }
                })
                .catch((renderError) => {
                  console.error('Mermaid render promise error:', renderError);
                  handleMermaidRenderError(renderError, preview, content, mermaidDiv, id);
                });
            } else {
              // Fallback to synchronous result
              if (renderPromise && renderPromise.svg) {
                mermaidDiv.innerHTML = renderPromise.svg;
                mermaidDiv.style.visibility = 'visible';
                previewRetryCount = 0;
                isUpdatingPreview = false;
              } else {
                throw new Error('Render API returned no result');
              }
            }
          } catch (renderApiError) {
            console.warn('Render API failed, falling back to init method:', renderApiError);

            // Fallback to init method with enhanced error handling
            try {
              // Ensure element is properly attached and visible in layout
              if (!document.body.contains(mermaidDiv)) {
                throw new Error('Element not in document body');
              }

              // Force a layout calculation to ensure dimensions are available
              mermaidDiv.offsetHeight; // Force reflow

              // Use init method as fallback
              mermaid.init(undefined, mermaidDiv);

              // Check result after a brief delay
              setTimeout(() => {
                if (mermaidDiv.querySelector('svg')) {
                  mermaidDiv.style.visibility = 'visible';
                  previewRetryCount = 0;
                } else {
                  preview.innerHTML = `<div class="error-message">
                    <strong>Unable to render diagram</strong><br>
                    Please check your Mermaid syntax.
                  </div>`;
                }
                isUpdatingPreview = false;
              }, 150);

            } catch (initError) {
              console.error('Init method also failed:', initError);
              handleMermaidRenderError(initError, preview, content, mermaidDiv, id);
            }
          }
        });
      } catch (error) {
        console.error('Error setting up Mermaid diagram:', error);
        preview.innerHTML = `<div class="error-message">
          <strong>Error setting up diagram:</strong><br>
          ${error.message}<br><br>
          <strong>Common issues to check:</strong><br>
          - Indentation and whitespace<br>
          - Special characters (try using simple ASCII characters)<br>
          - Missing connections or closing brackets<br>
          - Subgraph syntax (ensure proper nesting)<br>
        </div>`;
        isUpdatingPreview = false; // Reset flag
      }
    }

    // Auto-save draft functionality
    function autoSaveDraft() {
      const chartId = document.getElementById('chart-id').value;
      const title = document.getElementById('chart-title').value;
      const content = document.getElementById('chart-content').value;
      const isPublic = document.getElementById('chart-public').checked;
      const folder = document.getElementById('chart-folder').value;
      const notes = document.getElementById('chart-notes').value;
      const theme = document.getElementById('chart-theme').value;

      // Only auto-save if we have content and are editing an existing chart
      if (chartId && content.trim()) {
        const draftKey = `draft_${chartId}`;
        const draftData = {
          title: title,
          content: content,
          isPublic: isPublic,
          folder: folder,
          notes: notes,
          theme: theme,
          timestamp: Date.now()
        };

        try {
          localStorage.setItem(draftKey, JSON.stringify(draftData));
          showAutoSaveIndicator('Draft saved');
        } catch (error) {
          console.warn('Could not save draft:', error);
        }
      }
    }

    // Show auto-save indicator
    function showAutoSaveIndicator(message) {
      const indicator = document.getElementById('syntax-indicator');
      const originalContent = indicator.textContent;
      const originalClass = indicator.className;

      indicator.className = 'syntax-indicator checking';
      indicator.textContent = message;

      setTimeout(() => {
        indicator.className = originalClass;
        indicator.textContent = originalContent;
      }, 1500);
    }

    // Load draft if available
    function loadDraft(chartId) {
      const draftKey = `draft_${chartId}`;
      try {
        const draftData = localStorage.getItem(draftKey);
        if (draftData) {
          const draft = JSON.parse(draftData);
          // Check if draft is newer than 1 hour
          if (Date.now() - draft.timestamp < 3600000) {
            if (confirm('A recent draft was found. Would you like to load it?')) {
              document.getElementById('chart-title').value = draft.title;
              document.getElementById('chart-content').value = draft.content;
              document.getElementById('chart-public').checked = draft.isPublic;
              document.getElementById('chart-folder').value = draft.folder || '';
              document.getElementById('chart-notes').value = draft.notes || '';
              document.getElementById('chart-theme').value = draft.theme || currentMermaidTheme;

              // If draft has a different theme, switch to it
              if (draft.theme && draft.theme !== currentMermaidTheme) {
                changeMermaidTheme(draft.theme);
              }

              updateChartPreview();
            }
          }
          // Clean up old draft
          localStorage.removeItem(draftKey);
        }
      } catch (error) {
        console.warn('Could not load draft:', error);
      }
    }

    // Load folders for dropdown
    async function loadFolders() {
      try {
        const response = await fetch('/api/charts/folders/list', {
          credentials: 'include'
        });
        if (response.ok) {
          const data = await response.json();
          const select = document.getElementById('folder-suggestions');

          // Clear existing options except the first one
          while (select.children.length > 1) {
            select.removeChild(select.lastChild);
          }

          // Add folder options
          data.folders.forEach(folder => {
            const option = document.createElement('option');
            option.value = folder;
            option.textContent = folder;
            select.appendChild(option);
          });
        }
      } catch (error) {
        console.error('Error loading folders:', error);
      }
    }

    async function loadUserCharts() {
      try {
        // Use the getAllCharts function from charts.js to get all charts
        const charts = await getAllCharts();
        const chartsGrid = document.getElementById('charts-grid');
        const noChartsMessage = document.getElementById('no-charts-message');

        chartsGrid.innerHTML = '';

        if (charts && charts.length > 0) {
          noChartsMessage.style.display = 'none';

          charts.forEach(chart => {
            const chartCard = document.createElement('div');
            chartCard.className = 'chart-card';

            // Add folder info if present
            if (chart.folder) {
              const folderInfo = document.createElement('div');
              folderInfo.className = 'chart-folder';
              folderInfo.textContent = `📁 ${chart.folder}`;
              chartCard.appendChild(folderInfo);
            }

            // Create the card header with title
            const cardHeader = document.createElement('h4');
            cardHeader.textContent = chart.title;
            chartCard.appendChild(cardHeader);

            // Create the chart preview container with click handler for viewing
            const previewContainer = document.createElement('div');
            previewContainer.className = 'chart-preview-small';
            previewContainer.style.cursor = 'pointer';
            previewContainer.title = 'Click to view diagram';
            previewContainer.addEventListener('click', function() {
              viewChart(chart.id);
            });

            // Create a container for the rendered diagram
            const containerId = 'chart-preview-' + chart.id;
            previewContainer.id = containerId;
            chartCard.appendChild(previewContainer);

            // Sanitize the content for problematic Unicode characters
            const sanitizationResult = sanitizeMermaidContent(chart.content);
            const sanitizedContent = sanitizationResult.content;

            // Create a div with the mermaid class
            const mermaidDiv = document.createElement('div');
            mermaidDiv.className = 'mermaid';
            mermaidDiv.textContent = sanitizedContent;
            previewContainer.appendChild(mermaidDiv);

            // Use setTimeout to ensure the DOM is fully updated before rendering
            setTimeout(() => {
              try {
                // Initialize Mermaid on the new element
                mermaid.init(undefined, mermaidDiv);
              } catch (error) {
                console.error('Error rendering chart preview:', error);
                previewContainer.innerHTML = '<div class="error-message">Error rendering diagram</div>';
              }
            }, 100);

            // Create the actions container
            const actionsContainer = document.createElement('div');
            actionsContainer.className = 'chart-actions';
            actionsContainer.innerHTML = `
              <button class="nui-button small view-chart" data-id="${chart.id}">View</button>
              <button class="nui-button small edit-chart" data-id="${chart.id}">Edit</button>
              <button class="nui-button small delete-chart" data-id="${chart.id}">Delete</button>
              <button class="nui-button small share-chart" data-id="${chart.share_id}">Share</button>
            `;
            chartCard.appendChild(actionsContainer);

            // Add notes if present
            if (chart.notes) {
              const notesInfo = document.createElement('div');
              notesInfo.className = 'chart-notes';
              notesInfo.textContent = chart.notes;
              chartCard.appendChild(notesInfo);
            }

            chartsGrid.appendChild(chartCard);
          });

          // Each chart is rendered individually using the render API

          // Add event listeners to buttons
          document.querySelectorAll('.view-chart').forEach(button => {
            button.addEventListener('click', function() {
              viewChart(this.getAttribute('data-id'));
            });
          });

          document.querySelectorAll('.edit-chart').forEach(button => {
            button.addEventListener('click', function() {
              editChart(this.getAttribute('data-id'));
            });
          });

          document.querySelectorAll('.delete-chart').forEach(button => {
            button.addEventListener('click', function() {
              deleteChart(this.getAttribute('data-id'));
            });
          });

          document.querySelectorAll('.share-chart').forEach(button => {
            button.addEventListener('click', function() {
              shareChart(this.getAttribute('data-id'));
            });
          });
        } else {
          noChartsMessage.style.display = 'block';
        }
      } catch (error) {
        console.error('Error loading charts:', error);
      }
    }

    async function saveChart(e) {
      e.preventDefault();

      const chartId = document.getElementById('chart-id').value;
      const title = document.getElementById('chart-title').value;
      const content = document.getElementById('chart-content').value;
      const isPublic = document.getElementById('chart-public').checked;
      const folder = document.getElementById('chart-folder').value;
      const notes = document.getElementById('chart-notes').value;
      const theme = document.getElementById('chart-theme').value;

      try {
        let url = '/api/charts';
        let method = 'POST';

        if (chartId) {
          url = `/api/charts/${chartId}`;
          method = 'PUT';
        }

        const response = await fetch(url, {
          method: method,
          headers: {
            'Content-Type': 'application/json'
          },
          credentials: 'include',
          body: JSON.stringify({ title, content, isPublic, folder, notes, theme })
        });

        if (!response.ok) {
          throw new Error('Failed to save chart');
        }

        // Track chart creation/update
        if (window.analytics) {
          if (chartId) {
            window.analytics.trackEvent('chart_updated', {
              event_category: 'engagement',
              chart_id: chartId
            });
          } else {
            window.analytics.trackChartCreated('mermaid');
          }
        }

        hideChartEditor();
        loadUserCharts();
        loadFolders(); // Refresh folder list
      } catch (error) {
        console.error('Error saving chart:', error);
      }
    }

    async function editChart(chartId) {
      try {
        console.log('Editing chart with ID:', chartId);
        const chart = await getChartById(chartId);
        console.log('Chart data received:', chart);

        if (!chart) {
          alert('Chart not found');
          return;
        }

        // Make sure we're getting the correct data structure
        const chartData = chart.chart || chart;
        console.log('Chart data to use:', chartData);

        // Set the form values FIRST
        document.getElementById('chart-id').value = chartData.id;
        document.getElementById('chart-title').value = chartData.title;
        document.getElementById('chart-content').value = chartData.content;
        document.getElementById('chart-public').checked = chartData.public === 1;
        document.getElementById('chart-folder').value = chartData.folder || '';
        document.getElementById('chart-notes').value = chartData.notes || '';
        document.getElementById('chart-theme').value = chartData.theme || currentMermaidTheme;
        document.getElementById('editor-title').textContent = 'Edit Diagram';

        // If the chart has a different theme, switch to it
        if (chartData.theme && chartData.theme !== currentMermaidTheme) {
          changeMermaidTheme(chartData.theme);
        }

        // THEN show the editor (this will call updateChartPreview with the correct content)
        showChartEditor(false);

        // Check for and load any available draft
        setTimeout(() => loadDraft(chartData.id), 100);
      } catch (error) {
        console.error('Error editing chart:', error);
      }
    }

    async function deleteChart(chartId) {
      if (!confirm('Are you sure you want to delete this diagram?')) {
        return;
      }

      try {
        const response = await fetch(`/api/charts/${chartId}`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json'
          },
          credentials: 'include'
        });

        if (!response.ok) {
          throw new Error('Failed to delete chart');
        }

        loadUserCharts();
      } catch (error) {
        console.error('Error deleting chart:', error);
      }
    }

    function shareChart(shareId) {
      const shareUrl = `${window.location.origin}/share.html?id=${shareId}`;

      // Create a temporary input to copy the URL
      const tempInput = document.createElement('input');
      tempInput.value = shareUrl;
      document.body.appendChild(tempInput);
      tempInput.select();
      document.execCommand('copy');
      document.body.removeChild(tempInput);

      // Track chart sharing
      if (window.analytics) {
        window.analytics.trackChartShared('link');
      }

      alert('Share URL copied to clipboard: ' + shareUrl);
    }

    // View chart in modal
    async function viewChart(chartId) {
      try {
        console.log('Viewing chart with ID:', chartId);
        // Get the chart data
        const chart = await getChartById(chartId);
        console.log('Chart data received for view:', chart);

        if (!chart) {
          alert('Chart not found');
          return;
        }

        // Make sure we're getting the correct data structure
        const chartData = chart.chart || chart;
        console.log('Chart data to use for view:', chartData);

        // Set the chart title
        document.getElementById('view-chart-title').textContent = chartData.title;

        // Set data attributes for the action buttons
        document.getElementById('view-edit-chart').setAttribute('data-id', chartData.id);
        document.getElementById('view-delete-chart').setAttribute('data-id', chartData.id);
        document.getElementById('view-share-chart').setAttribute('data-id', chartData.share_id);

        // Show the modal first to ensure the container is visible
        document.getElementById('view-chart-modal').style.display = 'block';
        document.body.classList.add('modal-open');

        // Get the container
        const container = document.getElementById('view-chart-container');
        container.innerHTML = '';

        // Clean up any existing zoom controls
        cleanupZoomControls();

        // Sanitize the content for problematic Unicode characters
        const sanitizationResult = sanitizeMermaidContent(chartData.content);
        const sanitizedContent = sanitizationResult.content;

        // Create a div with the mermaid class
        const mermaidDiv = document.createElement('div');
        mermaidDiv.className = 'mermaid';
        mermaidDiv.textContent = sanitizedContent;
        container.appendChild(mermaidDiv);

        // Use enhanced rendering approach for modal view
        renderMermaidInModal(container, mermaidDiv, sanitizedContent);
      } catch (error) {
        console.error('Error viewing chart:', error);
        alert('Error viewing chart: ' + error.message);
      }
    }

    // Toggle fullscreen view
    function toggleViewFullscreen() {
      const modal = document.getElementById('view-chart-modal');
      const button = document.getElementById('modal-fullscreen-toggle');

      if (modal.classList.contains('fullscreen')) {
        // Exit fullscreen
        modal.classList.remove('fullscreen');
        button.textContent = '⛶';
        button.title = 'Toggle Fullscreen';
        document.body.style.overflow = '';
      } else {
        // Enter fullscreen
        modal.classList.add('fullscreen');
        button.textContent = '⛶';
        button.title = 'Exit Fullscreen';
        document.body.style.overflow = 'hidden';
      }
    }

    // Hide the view modal
    function hideViewModal() {
      const modal = document.getElementById('view-chart-modal');
      modal.style.display = 'none';
      document.body.classList.remove('modal-open');

      // Exit fullscreen if active
      if (modal.classList.contains('fullscreen')) {
        modal.classList.remove('fullscreen');
        document.getElementById('modal-fullscreen-toggle').textContent = '⛶';
        document.body.style.overflow = '';
      }

      // Reset modal zoom
      modalCurrentZoom = 1.0;
      updateModalZoom();

      // Clean up any dynamically created zoom controls (not the built-in ones)
      cleanupZoomControls();
    }

    // Function to fetch current user from session
    async function fetchCurrentUser() {
      try {
        const response = await fetch('/api/users/me', {
          credentials: 'include'
        });
        if (!response.ok) {
          if (response.status === 401) {
            // Not authenticated
            return null;
          }
          throw new Error('Failed to fetch current user');
        }

        const data = await response.json();
        return data.user;
      } catch (error) {
        console.error('Error fetching current user:', error);
        return null;
      }
    }
  </script>
</body>
</html>
